#extend("base"):
#export("content"):
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary mb-3">
                        <i class="fas fa-cogs me-3"></i>MatchIQ Admin
                    </h1>
                    <p class="lead text-muted">Database management and staff access</p>
                </div>

                <!-- Staff Login Section -->
                <div class="row mb-5">
                    <div class="col-lg-6 mx-auto">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user-shield me-2"></i>Staff Access
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="loginSection">
                                    <div class="alert alert-info mb-3">
                                        <small>
                                            <strong>Demo Staff Account:</strong><br>
                                            Email: <code><EMAIL></code><br>
                                            Password: <code>password123</code>
                                        </small>
                                    </div>
                                    <form id="staffLoginForm">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="password" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="password" name="password" value="password123" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-sign-in-alt me-2"></i>Login as Staff
                                        </button>
                                    </form>

                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="createStaffUser()">
                                            <i class="fas fa-user-plus me-1"></i>Create Demo Staff User
                                        </button>
                                    </div>
                                </div>

                                <div id="loggedInSection" style="display: none;">
                                    <div class="text-center">
                                        <div class="alert alert-success mb-3">
                                            <i class="fas fa-check-circle me-2"></i>
                                            Logged in as: <strong id="userEmail"></strong>
                                        </div>
                                        <div class="d-grid gap-2">
                                            <a href="/staff/dashboard" class="btn btn-success">
                                                <i class="fas fa-tachometer-alt me-2"></i>Staff Dashboard
                                            </a>
                                            <a href="/clients" class="btn btn-outline-primary">
                                                <i class="fas fa-users me-2"></i>View Clients & Matches
                                            </a>
                                            <a href="/caregivers" class="btn btn-outline-success">
                                                <i class="fas fa-user-nurse me-2"></i>View Caregivers & Matches
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" onclick="logout()">
                                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Database Seeding Section -->
                <div class="text-center mb-4">
                    <h2 class="h4 text-secondary">
                        <i class="fas fa-database me-2"></i>Database Seeding
                    </h2>
                    <p class="text-muted">Manage your MatchIQ database with sample data</p>
                </div>

                <!-- Current Status Card -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Current Database Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="stat-item">
                                    <h3 class="text-primary">#(status.clients)</h3>
                                    <small class="text-muted">Clients</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stat-item">
                                    <h3 class="text-success">#(status.caregivers)</h3>
                                    <small class="text-muted">Caregivers</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stat-item">
                                    <h3 class="text-info">#(status.questions)</h3>
                                    <small class="text-muted">Questions</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-warning">#(status.clientResponses)</h3>
                                    <small class="text-muted">Client Responses</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-secondary">#(status.caregiverResponses)</h3>
                                    <small class="text-muted">Caregiver Responses</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Action Cards -->
                    <div class="row">
                        <!-- Seed All -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-seedling fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">Seed All Data</h5>
                                    <p class="card-text text-muted">
                                        Add 30 sample clients and 30 sample caregivers with realistic responses
                                    </p>
                                    <button class="btn btn-success btn-lg" onclick="seedData('all')">
                                        <i class="fas fa-plus me-2"></i>Seed All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Reset & Seed -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-redo fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">Reset & Seed</h5>
                                    <p class="card-text text-muted">
                                        Clear all existing data and create fresh sample data
                                    </p>
                                    <button class="btn btn-primary btn-lg" onclick="resetAndSeed()">
                                        <i class="fas fa-refresh me-2"></i>Reset & Seed
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Seed Clients Only -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user fa-3x text-info"></i>
                                    </div>
                                    <h5 class="card-title">Seed Clients Only</h5>
                                    <p class="card-text text-muted">
                                        Add 30 sample clients with questionnaire responses
                                    </p>
                                    <button class="btn btn-info btn-lg" onclick="seedData('clients')">
                                        <i class="fas fa-user-plus me-2"></i>Seed Clients
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Seed Caregivers Only -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user-nurse fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">Seed Caregivers Only</h5>
                                    <p class="card-text text-muted">
                                        Add 30 sample caregivers with questionnaire responses
                                    </p>
                                    <button class="btn btn-warning btn-lg" onclick="seedData('caregivers')">
                                        <i class="fas fa-user-plus me-2"></i>Seed Caregivers
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Danger Zone -->
                    <div class="card border-danger mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                <strong>Warning:</strong> This action will permanently delete all clients, caregivers, and their responses.
                            </p>
                            <button class="btn btn-outline-danger" onclick="clearAllData()">
                                <i class="fas fa-trash me-2"></i>Clear All Data
                            </button>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="text-center">
                        #if(isStaff):
                            <a href="/clients" class="btn btn-outline-primary me-2">
                                <i class="fas fa-users me-2"></i>View Clients
                            </a>
                            <a href="/caregivers" class="btn btn-outline-success me-2">
                                <i class="fas fa-user-nurse me-2"></i>View Caregivers
                            </a>
                        #endif
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 id="loadingText">Processing...</h5>
                    <p class="text-muted mb-0">This may take a few moments</p>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stat-item {
            padding: 1rem;
        }
        .stat-item h3 {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
    </style>

    <script>
        // Check login status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();

            // Setup login form
            document.getElementById('staffLoginForm').addEventListener('submit', handleLogin);
        });

        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/status', {
                    method: 'GET',
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.authenticated && data.user && data.user.role === 'staff') {
                        showLoggedInState(data.user);
                    } else {
                        showLoginForm();
                    }
                } else {
                    showLoginForm();
                }
            } catch (error) {
                console.error('Error checking login status:', error);
                showLoginForm();
            }
        }

        function showLoggedInState(user) {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('loggedInSection').style.display = 'block';
            document.getElementById('userEmail').textContent = user.email;
        }

        function showLoginForm() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('loggedInSection').style.display = 'none';
        }

        async function handleLogin(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const email = formData.get('email');
            const password = formData.get('password');

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        email: email,
                        password: password
                    }),
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    // Check if user is staff
                    const statusResponse = await fetch('/auth/status', {
                        method: 'GET',
                        credentials: 'same-origin'
                    });

                    if (statusResponse.ok) {
                        const data = await statusResponse.json();
                        if (data.authenticated && data.user && data.user.role === 'staff') {
                            showNotification('Login successful!', 'success');
                            showLoggedInState(data.user);
                        } else {
                            showNotification('Access denied. Staff role required.', 'error');
                            await logout();
                        }
                    }
                } else {
                    const errorData = await response.json().catch(() => ({ error: 'Login failed' }));
                    showNotification(errorData.error || 'Login failed', 'error');
                }
            } catch (error) {
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    showNotification('Logged out successfully', 'success');
                    showLoginForm();
                    // Clear form
                    document.getElementById('staffLoginForm').reset();
                } else {
                    showNotification('Logout failed', 'error');
                }
            } catch (error) {
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function createStaffUser() {
            try {
                const response = await fetch('/api/users/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123',
                        role: 'staff'
                    })
                });

                if (response.ok) {
                    showNotification('Demo staff user created successfully!', 'success');
                } else {
                    const errorData = await response.json().catch(() => ({ error: 'Failed to create user' }));
                    if (errorData.error && errorData.error.includes('already exists')) {
                        showNotification('Demo staff user already exists', 'info');
                    } else {
                        showNotification(errorData.error || 'Failed to create user', 'error');
                    }
                }
            } catch (error) {
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function seedData(type) {
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');

            loadingText.textContent = `Seeding ${type}...`;
            loadingModal.show();

            try {
                const response = await fetch(`/api/seed/${type}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                loadingModal.hide();

                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification(result.message || 'Seeding failed', 'error');
                }
            } catch (error) {
                loadingModal.hide();
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function resetAndSeed() {
            if (!confirm('This will delete ALL existing data and create new sample data. Are you sure?')) {
                return;
            }

            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');

            loadingText.textContent = 'Resetting and seeding database...';
            loadingModal.show();

            try {
                const response = await fetch('/api/seed/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                loadingModal.hide();

                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification(result.message || 'Reset failed', 'error');
                }
            } catch (error) {
                loadingModal.hide();
                showNotification('Error: ' + error.message, 'error');
            }
        }

        async function clearAllData() {
            if (!confirm('This will permanently delete ALL clients, caregivers, and responses. This action cannot be undone. Are you sure?')) {
                return;
            }

            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            const loadingText = document.getElementById('loadingText');

            loadingText.textContent = 'Clearing all data...';
            loadingModal.show();

            try {
                const response = await fetch('/api/seed/clear', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                loadingModal.hide();

                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification(result.message || 'Clear failed', 'error');
                }
            } catch (error) {
                loadingModal.hide();
                showNotification('Error: ' + error.message, 'error');
            }
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
#endexport
#endextend

#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-friends me-2 text-info"></i>All Users
                    </h1>
                    <p class="text-muted mb-0">Manage system users and access control</p>
                </div>
                <div>
                    <a href="/staff/dashboard" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <a href="/admin/seed" class="btn btn-info">
                        <i class="fas fa-plus me-1"></i>Add New User
                    </a>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">#(users.count)</h4>
                                    <p class="mb-0">Total Users</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-friends fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">
                                        #(clientCount ?? 0)
                                    </h4>
                                    <p class="mb-0">Clients</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">
                                        #(caregiverCount ?? 0)
                                    </h4>
                                    <p class="mb-0">Caregivers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-nurse fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">
                                        #(staffCount ?? 0)
                                    </h4>
                                    <p class="mb-0">Staff</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-shield fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>User Directory
                    </h5>
                </div>
                <div class="card-body">
                    #if(users):
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(user in users):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm 
                                                        #if(user.role == "client"):
                                                            bg-primary
                                                        #elseif(user.role == "caregiver"):
                                                            bg-success
                                                        #elseif(user.role == "staff"):
                                                            bg-warning
                                                        #else:
                                                            bg-secondary
                                                        #endif
                                                        text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        #if(user.role == "client"):
                                                            <i class="fas fa-user"></i>
                                                        #elseif(user.role == "caregiver"):
                                                            <i class="fas fa-user-nurse"></i>
                                                        #elseif(user.role == "staff"):
                                                            <i class="fas fa-user-shield"></i>
                                                        #else:
                                                            <i class="fas fa-user"></i>
                                                        #endif
                                                    </div>
                                                    <strong>#(user.name)</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="mailto:#(user.email)" class="text-decoration-none">
                                                    #(user.email)
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge 
                                                    #if(user.role == "client"):
                                                        bg-primary
                                                    #elseif(user.role == "caregiver"):
                                                        bg-success
                                                    #elseif(user.role == "staff"):
                                                        bg-warning text-dark
                                                    #else:
                                                        bg-secondary
                                                    #endif
                                                ">
                                                    #if(user.role == "client"):
                                                        Client
                                                    #elseif(user.role == "caregiver"):
                                                        Caregiver
                                                    #elseif(user.role == "staff"):
                                                        Staff
                                                    #else:
                                                        #(user.role)
                                                    #endif
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    #date(user.createdAt, "MMM d, yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    #if(user.role == "client"):
                                                        <a href="/client/#(user.id)" class="btn btn-outline-primary" title="View Profile">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    #elseif(user.role == "caregiver"):
                                                        <a href="/caregiver/#(user.id)" class="btn btn-outline-success" title="View Profile">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    #endif
                                                    <button class="btn btn-outline-danger" title="Delete User" onclick="deleteUser('#(user.id)', '#(user.name)')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Users Found</h5>
                            <p class="text-muted">No users have been created yet.</p>
                            <a href="/admin/seed" class="btn btn-info">
                                <i class="fas fa-plus me-1"></i>Create First User
                            </a>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Deletion</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete user <strong id="userName"></strong>?</p>
                        <p class="text-danger"><small>This action cannot be undone and will remove all associated data.</small></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDelete">Delete User</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let userToDelete = null;

            function deleteUser(userId, userName) {
                userToDelete = userId;
                document.getElementById('userName').textContent = userName;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            document.getElementById('confirmDelete').addEventListener('click', function() {
                if (userToDelete) {
                    // TODO: Implement delete functionality
                    alert('Delete functionality will be implemented soon.');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                }
            });
        </script>

        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
        </style>
    #endexport
    
    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend
